from django.core.management.base import BaseCommand
from django.utils import timezone
from notifications.models import Notification
import pytz
from datetime import datetime


class Command(BaseCommand):
    help = 'Fix notification timestamps from UTC to Europe/Paris timezone'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        # Get UTC and Paris timezones
        utc_tz = pytz.UTC
        paris_tz = pytz.timezone('Europe/Paris')
        
        # Get all notifications
        notifications = Notification.objects.all()
        
        self.stdout.write(f"Found {notifications.count()} notifications to process")
        
        updated_count = 0
        
        for notification in notifications:
            # Convert created_at from UTC to Paris time
            if notification.created_at.tzinfo is None:
                # If naive datetime, assume it's UTC
                utc_time = utc_tz.localize(notification.created_at)
            else:
                # If timezone-aware, convert to UTC first
                utc_time = notification.created_at.astimezone(utc_tz)
            
            # Convert to Paris timezone
            paris_time = utc_time.astimezone(paris_tz)
            
            # Convert back to naive datetime (Django will handle timezone)
            new_created_at = paris_time.replace(tzinfo=None)
            
            # Handle read_at if it exists
            new_read_at = None
            if notification.read_at:
                if notification.read_at.tzinfo is None:
                    utc_read_time = utc_tz.localize(notification.read_at)
                else:
                    utc_read_time = notification.read_at.astimezone(utc_tz)
                
                paris_read_time = utc_read_time.astimezone(paris_tz)
                new_read_at = paris_read_time.replace(tzinfo=None)
            
            if dry_run:
                self.stdout.write(
                    f"Notification {notification.id}: "
                    f"{notification.created_at} -> {new_created_at}"
                )
                if notification.read_at:
                    self.stdout.write(
                        f"  Read at: {notification.read_at} -> {new_read_at}"
                    )
            else:
                # Update the notification
                notification.created_at = new_created_at
                if new_read_at:
                    notification.read_at = new_read_at
                notification.save()
                updated_count += 1
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f"DRY RUN: Would update {notifications.count()} notifications"
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully updated {updated_count} notifications"
                )
            )
